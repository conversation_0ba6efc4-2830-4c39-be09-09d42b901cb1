# 基础镜像
FROM  hub.inksyun.com/inksdev/openjdk:8-jre
# author
MAINTAINER inks

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>/home/<USER>/logs
# 指定路径
WORKDIR /home/<USER>

# JVM内存和GC优化参数
ENV JVM_OPTS="-Xms512m -Xmx2g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
    -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m \
    -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime \
    -Xloggc:/home/<USER>/logs/gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M \
    -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/logs/ \
    -XX:+PrintClassHistogram -XX:+TraceClassLoading -XX:+TraceClassUnloading"

ENV PARAMS="--server.port=8080 --spring.profiles.active=prod"
RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

# 复制jar文件到路径
COPY ./target/*.jar /home/<USER>/app.jar
EXPOSE 8080
# 启动文件服务
ENTRYPOINT ["/bin/sh","-c","java -Dfile.encoding=utf8 -Djava.security.egd=file:/dev/./urandom ${JVM_OPTS} -jar app.jar ${PARAMS}"]