package inks.oms.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.management.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JVM监控控制器
 * 提供JVM内存、GC、类加载等详细信息的API接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Api(tags = "JVM监控管理")
@RestController
@RequestMapping("/api/monitoring")
public class MonitoringController {

    /**
     * 获取JVM内存详细信息
     */
    @ApiOperation("获取JVM内存详情")
    @GetMapping("/memory")
    public Map<String, Object> getMemoryInfo() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取内存管理Bean
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        // 堆内存信息
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        Map<String, Object> heapInfo = new HashMap<>();
        heapInfo.put("init", formatBytes(heapMemory.getInit()));
        heapInfo.put("used", formatBytes(heapMemory.getUsed()));
        heapInfo.put("committed", formatBytes(heapMemory.getCommitted()));
        heapInfo.put("max", formatBytes(heapMemory.getMax()));
        heapInfo.put("usagePercent", String.format("%.2f%%", 
            (double) heapMemory.getUsed() / heapMemory.getMax() * 100));
        
        // 非堆内存信息（元空间等）
        MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
        Map<String, Object> nonHeapInfo = new HashMap<>();
        nonHeapInfo.put("init", formatBytes(nonHeapMemory.getInit()));
        nonHeapInfo.put("used", formatBytes(nonHeapMemory.getUsed()));
        nonHeapInfo.put("committed", formatBytes(nonHeapMemory.getCommitted()));
        nonHeapInfo.put("max", formatBytes(nonHeapMemory.getMax()));
        
        // 内存池详细信息
        List<MemoryPoolMXBean> memoryPools = ManagementFactory.getMemoryPoolMXBeans();
        Map<String, Object> poolsInfo = new HashMap<>();
        for (MemoryPoolMXBean pool : memoryPools) {
            MemoryUsage usage = pool.getUsage();
            if (usage != null) {
                Map<String, Object> poolInfo = new HashMap<>();
                poolInfo.put("type", pool.getType().toString());
                poolInfo.put("used", formatBytes(usage.getUsed()));
                poolInfo.put("committed", formatBytes(usage.getCommitted()));
                poolInfo.put("max", formatBytes(usage.getMax()));
                poolsInfo.put(pool.getName(), poolInfo);
            }
        }
        
        result.put("heap", heapInfo);
        result.put("nonHeap", nonHeapInfo);
        result.put("memoryPools", poolsInfo);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取GC活动信息
     */
    @ApiOperation("获取GC活动详情")
    @GetMapping("/gc")
    public Map<String, Object> getGcInfo() {
        Map<String, Object> result = new HashMap<>();
        
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        Map<String, Object> gcInfo = new HashMap<>();
        
        long totalCollectionCount = 0;
        long totalCollectionTime = 0;
        
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            Map<String, Object> gcDetail = new HashMap<>();
            gcDetail.put("collectionCount", gcBean.getCollectionCount());
            gcDetail.put("collectionTime", gcBean.getCollectionTime() + "ms");
            gcDetail.put("memoryPoolNames", gcBean.getMemoryPoolNames());
            
            totalCollectionCount += gcBean.getCollectionCount();
            totalCollectionTime += gcBean.getCollectionTime();
            
            gcInfo.put(gcBean.getName(), gcDetail);
        }
        
        result.put("collectors", gcInfo);
        result.put("totalCollectionCount", totalCollectionCount);
        result.put("totalCollectionTime", totalCollectionTime + "ms");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取类加载信息
     */
    @ApiOperation("获取类加载详情")
    @GetMapping("/classloading")
    public Map<String, Object> getClassLoadingInfo() {
        Map<String, Object> result = new HashMap<>();
        
        ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();
        
        result.put("loadedClassCount", classLoadingBean.getLoadedClassCount());
        result.put("totalLoadedClassCount", classLoadingBean.getTotalLoadedClassCount());
        result.put("unloadedClassCount", classLoadingBean.getUnloadedClassCount());
        result.put("isVerbose", classLoadingBean.isVerbose());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取线程信息
     */
    @ApiOperation("获取线程详情")
    @GetMapping("/threads")
    public Map<String, Object> getThreadInfo() {
        Map<String, Object> result = new HashMap<>();
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        
        result.put("threadCount", threadBean.getThreadCount());
        result.put("peakThreadCount", threadBean.getPeakThreadCount());
        result.put("totalStartedThreadCount", threadBean.getTotalStartedThreadCount());
        result.put("daemonThreadCount", threadBean.getDaemonThreadCount());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 获取系统运行时信息
     */
    @ApiOperation("获取系统运行时详情")
    @GetMapping("/runtime")
    public Map<String, Object> getRuntimeInfo() {
        Map<String, Object> result = new HashMap<>();
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        result.put("vmName", runtimeBean.getVmName());
        result.put("vmVersion", runtimeBean.getVmVersion());
        result.put("vmVendor", runtimeBean.getVmVendor());
        result.put("uptime", formatDuration(runtimeBean.getUptime()));
        result.put("startTime", runtimeBean.getStartTime());
        result.put("inputArguments", runtimeBean.getInputArguments());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 触发垃圾回收（谨慎使用）
     */
    @ApiOperation("手动触发GC")
    @GetMapping("/gc/trigger")
    public Map<String, Object> triggerGc() {
        Map<String, Object> result = new HashMap<>();
        
        long beforeUsed = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
        System.gc();
        
        // 等待一小段时间让GC完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long afterUsed = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
        
        result.put("beforeGC", formatBytes(beforeUsed));
        result.put("afterGC", formatBytes(afterUsed));
        result.put("freed", formatBytes(beforeUsed - afterUsed));
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 格式化字节数为可读格式
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "N/A";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 格式化持续时间
     */
    private String formatDuration(long millis) {
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天 %d小时 %d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时 %d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟 %d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
