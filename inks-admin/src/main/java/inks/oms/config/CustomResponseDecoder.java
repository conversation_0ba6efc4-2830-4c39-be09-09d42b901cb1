package inks.oms.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import feign.Response;
import feign.codec.Decoder;
import inks.common.core.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.ParameterizedType;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * Feign解码器，
 * 自定义 用于解析响应数据并将其映射到泛型对象 R<T>。
 * 不写解码器会报错："inks.common.core.domain.R<java.util.List<inks.common.core.domain.DeptinfoPojo>> is not a type supported by this decoder.",
 */
class CustomResponseDecoder implements Decoder {
    private static final Logger log = LoggerFactory.getLogger(CustomResponseDecoder.class);
    private final ObjectMapper objectMapper;

    // 构造方法，注入 ObjectMapper，用于处理 JSON 数据的解析
    public CustomResponseDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        this.objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        // 使用 Jackson SimpleModule 自定义 Date 解析
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Date.class, new DateDeserializers.DateDeserializer());
        this.objectMapper.registerModule(module);
    }


    @Override
    public Object decode(Response response, java.lang.reflect.Type type) throws IOException {
        String rawResponse = "";
        try (InputStream responseBody = response.body().asInputStream()) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = responseBody.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
            rawResponse = byteArrayOutputStream.toString(StandardCharsets.UTF_8.name());
            log.info("Feign解码器 原始响应数据: {}", rawResponse);
        } catch (Exception e) {
            log.error("Feign解码器 读取响应数据时出错: ", e);
            throw e;
        }

        Object decodedObject;
        if (!(type instanceof ParameterizedType)) {
            decodedObject = objectMapper.readValue(rawResponse, objectMapper.constructType(type));
        } else {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();

            if (R.class.isAssignableFrom(rawType)) {
                Map<String, Object> responseMap = objectMapper.readValue(rawResponse, new TypeReference<Map<String, Object>>() {
                });

                R<Object> result = new R<>();
                result.setCode((Integer) responseMap.get("code"));
                result.setMsg((String) responseMap.get("msg")); // msg直接作为普通字符串处理

                Object data = responseMap.get("data");
                if (data != null) {
                    JavaType javaType = objectMapper.constructType(parameterizedType.getActualTypeArguments()[0]);
                    result.setData(objectMapper.convertValue(data, javaType));
                }
                decodedObject = result;
            } else {
                decodedObject = objectMapper.readValue(rawResponse, objectMapper.constructType(type));
            }
        }

        log.info("Feign解码器 解码后数据: {}", objectMapper.writeValueAsString(decodedObject));
        return decodedObject;
    }
}