package inks.oms.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 路由前缀配置类
 */
@Configuration
public class RoutePrefixConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 为指定包名全匹配Controller： 添加路径前缀
        configurer.addPathPrefix("/auth", clazz -> clazz.getPackage().getName().equals("inks.auth.controller"));
        configurer.addPathPrefix("/system", clazz -> clazz.getPackage().getName().equals("inks.system.controller"));
        configurer.addPathPrefix("/utils", clazz -> clazz.getPackage().getName().equals("inks.service.std.utils.controller"));
        configurer.addPathPrefix("/sale", clazz -> clazz.getPackage().getName().equals("inks.service.std.sale.controller"));
        configurer.addPathPrefix("/goods", clazz -> clazz.getPackage().getName().equals("inks.service.std.goods.controller"));
        configurer.addPathPrefix("/buy", clazz -> clazz.getPackage().getName().equals("inks.service.std.buy.controller"));
        configurer.addPathPrefix("/manu", clazz -> clazz.getPackage().getName().equals("inks.service.std.manu.controller"));
        configurer.addPathPrefix("/store", clazz -> clazz.getPackage().getName().equals("inks.service.std.store.controller"));
        configurer.addPathPrefix("/qms", clazz -> clazz.getPackage().getName().equals("inks.service.std.qms.controller"));
        configurer.addPathPrefix("/fm", clazz -> clazz.getPackage().getName().equals("inks.service.std.fm.controller"));
        configurer.addPathPrefix("/eam", clazz -> clazz.getPackage().getName().equals("inks.service.std.eam.controller"));
    }
}
