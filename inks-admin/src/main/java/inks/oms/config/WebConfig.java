package inks.oms.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Swagger UI静态资源配置
 * 通过 WebMvcConfigurer 配置 Spring MVC，使得 Swagger UI 的静态资源能够正确加载，从而解决 swagger-ui.html 无法打开的问题。
 * 移入utils后 swagger一直打不开,http://localhost:10681/v2/api可以打开api,但是http://localhost:10681/swagger-ui.html不行
 * 加入以下配置才行
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}

