package inks.oms.config;

import inks.system.utils.PrintColor;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 打印项目启动所有的Bean信息
 */
@Configuration
public class BeanPrinterConfig {

    @Bean
    public ApplicationRunner printAllBeans(ApplicationContext applicationContext) {
        return args -> {
            String[] beanNames = applicationContext.getBeanDefinitionNames();
            PrintColor.zi("========== All Beans in ApplicationContext ==========");
            //int index = 1;
            //for (String beanName : beanNames) {
            //    Object bean = applicationContext.getBean(beanName);
            //    PrintColor.zi("No."+index+" Bean Name: " + beanName + " -> Bean Class: " + bean.getClass().getName());
            //    index++;
            //}
            PrintColor.zi("================共注册进入" + beanNames.length + "个Bean================");
        };
    }
}
