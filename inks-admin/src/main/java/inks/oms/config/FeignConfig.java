package inks.oms.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.RequestInterceptor;
import feign.Target;
import inks.api.feign.*;
import inks.system.utils.PrintColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * FeignConfig 配置类
 */
@Configuration
@ConditionalOnProperty(name = "inks.svcfeign")
@Import(FeignUrlCorrector.class)  // 导入URL修正器
public class FeignConfig {
    private static final Logger log = LoggerFactory.getLogger(FeignConfig.class);
    private final Map<Class<?>, String> feignClientMapping = new HashMap<>();
    @Value("${inks.svcfeign:}")
    private String svcfeign;

    private <T> T createFeignClient(Class<T> clientClass, String url) {
        log.info("创建 Feign 客户端: {}, 目标地址: {}", clientClass.getSimpleName(), url);
        return Feign.builder()
                .contract(new SpringMvcContract())
                .decoder(new CustomResponseDecoder(new ObjectMapper())) // 解码器 统一格式R<>
                .requestInterceptor(requestInterceptor())    // 显式添加拦截器 打印Feign请求信息
                .target(clientClass, url);
    }
    @Bean
    public RequestInterceptor requestInterceptor() {
        return template -> {
            template.header("Content-Type", "application/json; charset=UTF-8");
            Target<?> target = template.feignTarget();
            String fullUrl = target.url() + template.url();
            log.info("Feign 请求: {} {}", template.method(), fullUrl);
            log.info("请求头: {}", template.headers());

            byte[] bodyBytes = template.body();
            if (bodyBytes != null) {
                String body = new String(bodyBytes, StandardCharsets.UTF_8);
                log.info("请求体: {}", body);
            } else {
                log.info("请求体为空");
            }
        };
    }
    @PostConstruct
    public void init() {
        feignClientMapping.put(AuthFeignService.class, "/auth");
        feignClientMapping.put(SystemFeignService.class, "/system");
        feignClientMapping.put(UtilsFeignService.class, "/utils");
        feignClientMapping.put(QmsFeignService.class, "/qms");
        feignClientMapping.put(FileFeignService.class, "/file");
        feignClientMapping.put(GoodsFeignService.class, "/goods");
        feignClientMapping.put(ManuFeignService.class, "/manu");
        feignClientMapping.put(StoreFeignService.class, "/store");
        feignClientMapping.put(JobFeignService.class, "/job");

        PrintColor.red("FeignConfig 初始化完成: " + svcfeign);
    }

    //@Bean
    //public RequestInterceptor requestInterceptor() {
    //    return template -> {
    //        template.header("Content-Type", "application/json; charset=UTF-8");
    //        Target<?> target = template.feignTarget();
    //        String fullUrl = target.url() + template.url();
    //        log.info("Feign 请求: {} {}", template.method(), fullUrl);
    //        log.info("请求头: {}", template.headers());
    //
    //        byte[] bodyBytes = template.body();
    //        if (bodyBytes != null) {
    //            String body = new String(bodyBytes, StandardCharsets.UTF_8);
    //            log.info("请求体: {}", body);
    //        } else {
    //            log.info("请求体为空");
    //        }
    //    };
    //}

    private String getUrl(Class<?> clientClass) {
        String endpoint = feignClientMapping.get(clientClass);
        if (endpoint == null) {
            throw new IllegalArgumentException("未配置 Feign 客户端: " + clientClass.getSimpleName());
        }
        return svcfeign + endpoint;
    }

    @Bean
    public AuthFeignService authFeignService() {
        return createFeignClient(AuthFeignService.class, getUrl(AuthFeignService.class));
    }

    @Bean
    public SystemFeignService systemFeignService() {
        return createFeignClient(SystemFeignService.class, getUrl(SystemFeignService.class));
    }

    @Bean
    public UtilsFeignService utilsFeignService() {
        return createFeignClient(UtilsFeignService.class, getUrl(UtilsFeignService.class));
    }

    @Bean
    public QmsFeignService qmsFeignService() {
        return createFeignClient(QmsFeignService.class, getUrl(QmsFeignService.class));
    }

    @Bean
    public FileFeignService fileFeignService() {
        return createFeignClient(FileFeignService.class, getUrl(FileFeignService.class));
    }

    @Bean
    public GoodsFeignService goodsFeignService() {
        return createFeignClient(GoodsFeignService.class, getUrl(GoodsFeignService.class));
    }

    @Bean
    public ManuFeignService manuFeignService() {
        return createFeignClient(ManuFeignService.class, getUrl(ManuFeignService.class));
    }

    @Bean
    public StoreFeignService storeFeignService() {
        return createFeignClient(StoreFeignService.class, getUrl(StoreFeignService.class));
    }

    @Bean
    public JobFeignService jobFeignService() {
        return createFeignClient(JobFeignService.class, getUrl(JobFeignService.class));
    }

}



