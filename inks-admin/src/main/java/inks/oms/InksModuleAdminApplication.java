package inks.oms;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;


// 如果不显式指定 @MapperScan 注解，默认情况下，Spring Boot MyBatis 只会扫描启动类所在的包及其子包中的 Mapper 接口。
@SpringBootApplication(scanBasePackages = {"inks.oms", "inks.api.feign", "inks.common.core", "inks.common.redis", "inks.common.security", "inks.common.log"})
@MapperScan("inks.oms.mapper")
@EnableSwagger2
@EnableFeignClients("inks.api")
@EnableAsync
public class InksModuleAdminApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(InksModuleAdminApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" : property;
        String dbUrl = env.getProperty("spring.datasource.dynamic.datasource.inkssaas.url");

        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application Sailrui-Boot is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:" + port + path + "/swagger-ui.html\n\t" +
                        "External: \thttp://" + ip + ":" + port + path + "/swagger-ui.html\n\t" +
                        "Web: \t\thttp://" + ip + ":" + port + path + "\n\t" +
                        "Nacos: \t\thttp://dev.inksyun.com:" + port + "/swagger-ui.html\n\t" +
                        "Database URL: \t" + dbUrl + "\n\t" +
                        "------------------------------------------------------------");
    }
}
