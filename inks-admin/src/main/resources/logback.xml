<configuration>

    <!-- 定义控制台日志输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 设置日志格式 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 配置 MyBatis SQL 日志 -->
    <logger name="org.apache.ibatis" level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="org.mybatis" level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- 配置 MyBatis SQL 打印实现 -->
    <logger name="org.apache.ibatis.logging.stdout.StdOutImpl" level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- 配置 Spring JDBC 日志 -->
    <logger name="org.springframework.jdbc.core" level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- 配置事务日志 -->
    <logger name="org.springframework.transaction" level="DEBUG">
        <appender-ref ref="STDOUT"/>
    </logger>

    <!-- Spring 默认日志级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
