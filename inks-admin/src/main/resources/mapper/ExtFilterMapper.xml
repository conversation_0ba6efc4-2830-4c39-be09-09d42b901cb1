<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.oms.mapper.ExtFilterMapper">
    <!--货品And过滤-->
    <sql id="goodsandfilter">
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid  != ''">
            and Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname  != ''">
            and Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec  != ''">
            and Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid  != ''">
            and Mat_Goods.Partid like concat('%', #{SearchPojo.partid}, '%')
        </if>
    </sql>
    <!--货品Or过滤-->
    <sql id="goodsorfilter">
        <!--货品查询 -->
        <if test="SearchPojo.goodsuid != null and SearchPojo.goodsuid != ''">
            or Mat_Goods.GoodsUid like concat('%', #{SearchPojo.goodsuid}, '%')
        </if>
        <if test="SearchPojo.goodsname != null and SearchPojo.goodsname != ''">
            or Mat_Goods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null and SearchPojo.goodsspec != ''">
            or Mat_Goods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.partid != null and SearchPojo.partid != ''">
            or Mat_Goods.Partid like concat('%', #{SearchPojo.partid}, '%')
        </if>
    </sql>
    <!--往来单位And过滤-->
    <sql id="workgroupandfilter">
        <if test="SearchPojo.groupuid != null and SearchPojo.groupuid  != ''">
            and App_Workgroup.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname  != ''">
            and App_Workgroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
    </sql>
    <!--往来单位Or过滤-->
    <sql id="workgrouporfilter">
        <if test="SearchPojo.groupuid != null and SearchPojo.groupuid != ''">
            or App_Workgroup.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            or App_Workgroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
    </sql>
</mapper>

