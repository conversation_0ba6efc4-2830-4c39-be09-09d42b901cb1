# JVM监控专用配置文件
# 可以通过 --spring.profiles.active=dev,monitoring 激活

# 详细的监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: "GET,POST"
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
    env:
      enabled: true
    configprops:
      enabled: true
    beans:
      enabled: true
    mappings:
      enabled: true
    scheduledtasks:
      enabled: true
    threaddump:
      enabled: true
    heapdump:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s  # 指标收集间隔
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}
      instance: ${spring.application.name}-${random.value}
    web:
      server:
        request:
          autotime:
            enabled: true

# 日志配置 - 用于监控分析
logging:
  level:
    # GC日志相关
    org.springframework.boot.actuate: INFO
    io.micrometer: INFO
    # 内存泄漏检测
    org.springframework.context: DEBUG
    # 数据库连接池监控
    com.zaxxer.hikari: DEBUG
    # Redis连接监控  
    org.springframework.data.redis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: /home/<USER>/logs/application.log
    max-size: 100MB
    max-history: 30

# 数据库连接池监控配置
spring:
  datasource:
    hikari:
      # 连接池监控
      register-mbeans: true
      # 连接泄漏检测
      leak-detection-threshold: 60000  # 60秒
      # 连接超时时间
      connection-timeout: 30000
      # 连接最大生命周期
      max-lifetime: 1800000  # 30分钟
      # 连接空闲超时
      idle-timeout: 600000   # 10分钟

# Redis连接池监控配置  
  redis:
    lettuce:
      pool:
        # 启用连接池监控
        enabled: true
        # 连接池最大连接数
        max-active: 200
        # 连接池最大空闲连接数
        max-idle: 50
        # 连接池最小空闲连接数
        min-idle: 10
        # 连接池最大等待时间
        max-wait: 5000ms
      # 连接超时时间
      timeout: 6000ms

# 自定义监控配置
inks:
  monitoring:
    # 是否启用内存监控告警
    memory-alert:
      enabled: true
      # 堆内存使用率告警阈值
      heap-threshold: 85
      # 元空间使用率告警阈值  
      metaspace-threshold: 90
    # 是否启用GC监控告警
    gc-alert:
      enabled: true
      # GC时间告警阈值（毫秒）
      time-threshold: 1000
      # GC频率告警阈值（次/分钟）
      frequency-threshold: 10
    # 是否启用线程监控告警
    thread-alert:
      enabled: true
      # 线程数告警阈值
      count-threshold: 500
