server:
  port: 10681 #服务端口
spring:
  application:
    name: admin
  profiles:
    active: dev
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true

  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册

feign:
  sentinel:
    enabled: true
#mybatis:
#  mapper-locations: classpath:mapper/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    map-underscore-to-camel-case: true
#  type-aliases-package: inls.auth.domian

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# Spring Boot Actuator 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有监控端点
      base-path: /actuator
  endpoint:
    health:
      show-details: always  # 显示详细健康信息
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}

