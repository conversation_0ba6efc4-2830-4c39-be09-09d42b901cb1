spring:
  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  datasource:
    dynamic:
      primary: inkssaas #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        inkssaas:
          url: ******************************************************************************************************************************************
          username: root
          password: inks@8866
          driver-class-name: com.mysql.cj.jdbc.Driver # 3.2.0开始支持SPI可省略此配置
        flowable:
          url: ***********************************************************************************************************************************************************************
          username: root
          password: inks@8866
          driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 30
  cloud:
    nacos:
      discovery:
        enabled: false


  #redis配置
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: redis
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: inks@8866
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
    #    toEmail: <EMAIL>  #登录日志的收件人
    toEmail: <EMAIL>  #登录日志的收件人
    ipAddress: oms96  #ip地址
#  web: #配置静态资源访问路径
#    resources:
#      static-locations: classpath:/
#  mvc:
#    view:
#      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/**/*.xml  #资源文件分布在多个类路径中
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
logging:
  level:
    org.apache.ibatis: DEBUG
    org.springframework.jdbc.core: DEBUG
    org.springframework.transaction: DEBUG
    com.alibaba.druid: DEBUG

#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1

## MQTT##
mqtt:
  host: tcp://dev.inksyun.com:1883
  username: admin
  password: public
  qos: 2
  clientId: ClientId_96saoms_#{T(java.util.UUID).randomUUID().toString()}
  timeout: 10
  keepalive: 20
  enableTopic: 3    # 指定开启监听哪几个topic,逗号分隔如: 1,2 注意:项目启动后需要开启监听几个topic在此方法中定义: connectComplete(boolean reconnect, String serverURI)
  topic1: xapi/home/<USER>
  topic2: /#  #符号是代表整个/下面的全部子主题 不能直接使用#号
  topic3: inks/#
  topic4: $SYS/brokers/+/clients/#
# Grf转发打印接口
feign:
  client:
    url:
      GrfUrl: http://dev.inksyun.com:18801 # 公网:http://vip.inkstech.com:18801
# 调用oam公众号接口获取openid #内网测试号:http://*************:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
oam:
  api: http://*************:10677
  appid: wx58c9e35cc9fb9be5
oss:
  #  type: aliyun
  type: minio
  minio:
    bucket: utils
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://minio:9080
    urlprefix: http://dev.inksyun.com:9080/
  aliyun:
    bucket: inkstable
    access-key-id: LTAI5t7gvbML44MA1pxPbr21
    access-key-secret: ******************************
    endpoint: https://oss-cn-qingdao.aliyuncs.com
    urlprefix: https://inkstable.oss-cn-qingdao.aliyuncs.com/
flowable:
  async-executor-activate: false #关闭定时任务JOB
  # 将databaseSchemaUpdate 记得首次建库建表需设置为true。当Flowable发现库与数据库表结构不一致时,会自动将数据库表结构升级至新版本。
  database-schema-update: false
  process:
    engine:
      # 设置时区
      timezone: Asia/Shanghai

inks:
  svcfeign: http://*************:10681 #注意改为部署机器的ip地址！！！
  justauth:
    api: http://inks.tpddns.net:31080
    dingtalk:
      id: dingoa43wwh1kvqgpvangn
      secret: 1eGSxvhF6kCTruk66q4bzC3zZiDz7eVGBCCD9AMwHU_8Ja0NdrNiRcWDrwOOkpEN
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  feign:
    GrfUrl: dev.inksyun.com:18801 # 公网:http://vip.inkstech.com:18801
# 调用oam公众号接口获取openid #内网测试号:http://*************:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
