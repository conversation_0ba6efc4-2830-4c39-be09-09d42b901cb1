# JVM内存监控使用指南

## 快速开始

### 1. 启动应用时启用监控
```bash
# 开发环境
java -jar app.jar --spring.profiles.active=dev,monitoring

# 生产环境  
java -jar app.jar --spring.profiles.active=prod,monitoring
```

### 2. 访问监控端点

#### 基础监控端点
- **健康检查**: http://localhost:10681/actuator/health
- **指标概览**: http://localhost:10681/actuator/metrics
- **Prometheus指标**: http://localhost:10681/actuator/prometheus

#### 自定义监控API
- **内存详情**: http://localhost:10681/api/monitoring/memory
- **GC活动**: http://localhost:10681/api/monitoring/gc  
- **类加载**: http://localhost:10681/api/monitoring/classloading
- **线程信息**: http://localhost:10681/api/monitoring/threads
- **运行时信息**: http://localhost:10681/api/monitoring/runtime

## 内存问题诊断步骤

### 第一步：查看内存使用情况
```bash
curl http://localhost:10681/api/monitoring/memory
```

**关注指标**：
- `heap.usagePercent` - 堆内存使用率（>85%需要关注）
- `nonHeap.used` - 元空间使用量（持续增长可能有类加载问题）
- `memoryPools` - 各内存区域详情

### 第二步：分析GC活动
```bash
curl http://localhost:10681/api/monitoring/gc
```

**关注指标**：
- `totalCollectionCount` - 总GC次数
- `totalCollectionTime` - 总GC时间
- 各收集器的频率和耗时

### 第三步：检查类加载情况
```bash
curl http://localhost:10681/api/monitoring/classloading
```

**关注指标**：
- `loadedClassCount` - 当前加载的类数量
- `totalLoadedClassCount` - 总共加载过的类数量
- `unloadedClassCount` - 卸载的类数量

### 第四步：监控线程情况
```bash
curl http://localhost:10681/api/monitoring/threads
```

**关注指标**：
- `threadCount` - 当前线程数
- `peakThreadCount` - 峰值线程数

## 内存泄漏排查

### 1. 使用Actuator端点
```bash
# 生成堆转储文件
curl http://localhost:10681/actuator/heapdump -o heapdump.hprof

# 获取线程转储
curl http://localhost:10681/actuator/threaddump
```

### 2. 分析GC日志
GC日志位置：`/home/<USER>/logs/gc.log`

**关键指标**：
- Full GC频率增加
- 老年代内存回收效果差
- GC后内存使用率持续上升

### 3. 常见内存泄漏原因

#### 数据库连接泄漏
```bash
# 检查HikariCP连接池状态
curl http://localhost:10681/actuator/metrics/hikaricp.connections.active
curl http://localhost:10681/actuator/metrics/hikaricp.connections.pending
```

#### Redis连接泄漏
```bash
# 检查Redis连接池状态
curl http://localhost:10681/actuator/metrics/lettuce.command.completion
```

#### 缓存内存泄漏
```bash
# 检查缓存相关指标
curl http://localhost:10681/actuator/metrics | grep cache
```

## JVM参数优化建议

### 当前配置的JVM参数
```bash
-Xms512m                    # 初始堆内存512MB
-Xmx2g                      # 最大堆内存2GB  
-XX:MetaspaceSize=256m      # 初始元空间256MB
-XX:MaxMetaspaceSize=512m   # 最大元空间512MB
-XX:+UseG1GC               # 使用G1垃圾收集器
-XX:MaxGCPauseMillis=200   # 最大GC暂停时间200ms
```

### 根据监控结果调优

#### 如果堆内存不足
```bash
-Xms1g -Xmx4g  # 增加堆内存
```

#### 如果元空间不足
```bash
-XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=1g
```

#### 如果GC频繁
```bash
-XX:G1HeapRegionSize=32m    # 增加G1区域大小
-XX:MaxGCPauseMillis=100    # 降低暂停时间目标
```

## 监控告警设置

### 内存使用率告警
- 堆内存使用率 > 85%
- 元空间使用率 > 90%

### GC性能告警  
- 单次GC时间 > 1秒
- GC频率 > 10次/分钟

### 线程数告警
- 线程数 > 500

## 生产环境部署注意事项

1. **确保日志目录存在**：`/home/<USER>/logs/`
2. **监控端点安全**：生产环境建议限制访问IP
3. **资源配置**：确保容器有足够的内存（建议至少3GB）
4. **定期清理**：GC日志会自动轮转，但建议定期清理旧文件

## 故障排查清单

- [ ] 检查内存使用率是否正常
- [ ] 分析GC活动是否频繁
- [ ] 确认类加载数量是否异常增长
- [ ] 检查线程数是否过多
- [ ] 查看数据库连接池状态
- [ ] 检查Redis连接池状态
- [ ] 分析应用日志中的异常
- [ ] 生成并分析堆转储文件
