2025-07-23 16:50:49,009 - Initializing Velocity, Calling init()...
2025-07-23 16:50:49,009 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-23 16:50:49,009 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-23 16:50:49,009 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-23 16:50:49,009 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-23 16:50:49,009 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:50:49,009 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-23 16:50:49,017 - Resource<PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-23 16:50:49,019 - Do unicode file recognition:  false
2025-07-23 16:50:49,019 - FileResourceLoader : adding path '.'
2025-07-23 16:50:49,043 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-23 16:50:49,048 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-23 16:50:49,050 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-23 16:50:49,051 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-23 16:50:49,052 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-23 16:50:49,053 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-23 16:50:49,054 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-23 16:50:49,056 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-23 16:50:49,057 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-23 16:50:49,058 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-23 16:50:49,083 - Created '20' parsers.
2025-07-23 16:50:49,087 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-23 16:50:49,088 - Velocimacro : Default library not found.
2025-07-23 16:50:49,088 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-23 16:50:49,088 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-23 16:50:49,088 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-23 16:50:49,088 - Velocimacro : autoload off : VM system will not automatically reload global library macros
