2024-12-23 14:07:11,372 - Initializing Velocity, Calling init()...
2024-12-23 14:07:11,372 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:07:11,372 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:07:11,372 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:07:11,372 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-23 14:07:11,372 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:11,372 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:11,378 - <PERSON><PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:07:11,380 - Do unicode file recognition:  false
2024-12-23 14:07:11,380 - FileResourceLoader : adding path '.'
2024-12-23 14:07:11,393 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:07:11,396 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:07:11,398 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:07:11,400 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:07:11,401 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:07:11,402 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:07:11,404 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:07:11,406 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:07:11,408 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:07:11,410 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:07:11,427 - Created '20' parsers.
2024-12-23 14:07:11,430 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:07:11,430 - Velocimacro : Default library not found.
2024-12-23 14:07:11,430 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:07:11,430 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:07:11,430 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:07:11,430 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:07:11,452 - Null reference [template 'VelocityUtils', line 1, column 73] : $printlayer cannot be resolved.
2024-12-23 14:07:16,440 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:07:16,441 - Initializing Velocity, Calling init()...
2024-12-23 14:07:16,441 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:07:16,441 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:07:16,441 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:07:16,441 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:07:16,441 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:16,441 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:16,442 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:07:16,442 - Do unicode file recognition:  false
2024-12-23 14:07:16,442 - FileResourceLoader : adding path '.'
2024-12-23 14:07:16,442 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:07:16,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:07:16,443 - Created '20' parsers.
2024-12-23 14:07:16,443 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:07:16,443 - Velocimacro : Default library not found.
2024-12-23 14:07:16,443 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:07:16,443 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:07:16,443 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:07:16,443 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:07:16,444 - Null reference [template 'VelocityUtils', line 1, column 73] : $printlayer cannot be resolved.
cope if allowed.
2024-12-23 14:07:16,443 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:07:16,444 - Null reference [template 'VelocityUtils', line 1, column 73] : $printlayer cannot be resolved.
2024-12-23 14:07:55,750 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:07:55,753 - Initializing Velocity, Calling init()...
2024-12-23 14:07:55,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:07:55,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:07:55,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:07:55,753 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:07:55,753 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:55,753 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:07:55,753 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:07:55,753 - Do unicode file recognition:  false
2024-12-23 14:07:55,753 - FileResourceLoader : adding path '.'
2024-12-23 14:07:55,753 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:07:55,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:07:55,754 - Created '20' parsers.
2024-12-23 14:07:55,754 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:07:55,755 - Velocimacro : Default library not found.
2024-12-23 14:07:55,755 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:07:55,755 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:07:55,755 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:07:55,755 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:08:08,317 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:08:08,317 - Initializing Velocity, Calling init()...
2024-12-23 14:08:08,317 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:08:08,317 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:08:08,317 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:08:08,317 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:08:08,317 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:08:08,317 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:08:08,317 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:08:08,317 - Do unicode file recognition:  false
2024-12-23 14:08:08,317 - FileResourceLoader : adding path '.'
2024-12-23 14:08:08,318 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:08:08,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:08:08,319 - Created '20' parsers.
2024-12-23 14:08:08,319 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:08:08,319 - Velocimacro : Default library not found.
2024-12-23 14:08:08,319 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:08:08,319 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:08:08,319 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:08:08,319 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:09:28,808 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:09:28,808 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:09:28,809 - Initializing Velocity, Calling init()...
2024-12-23 14:09:28,809 - Initializing Velocity, Calling init()...
2024-12-23 14:09:28,809 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:09:28,809 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:09:28,809 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:09:28,809 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:09:28,809 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:09:28,809 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:09:28,809 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:09:28,809 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:09:28,809 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:28,809 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:28,809 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:28,809 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:28,809 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:09:28,809 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:09:28,809 - Do unicode file recognition:  false
2024-12-23 14:09:28,809 - Do unicode file recognition:  false
2024-12-23 14:09:28,809 - FileResourceLoader : adding path '.'
2024-12-23 14:09:28,809 - FileResourceLoader : adding path '.'
2024-12-23 14:09:28,809 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:09:28,809 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:09:28,810 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:09:28,810 - Created '20' parsers.
2024-12-23 14:09:28,810 - Created '20' parsers.
2024-12-23 14:09:28,811 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:09:28,811 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:09:28,811 - Velocimacro : Default library not found.
2024-12-23 14:09:28,811 - Velocimacro : Default library not found.
2024-12-23 14:09:28,811 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:09:28,811 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:09:28,811 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:09:28,811 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:09:28,811 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:09:28,811 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:09:28,811 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:09:28,811 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:09:38,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:09:38,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:09:38,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:09:38,001 - Initializing Velocity, Calling init()...
2024-12-23 14:09:38,001 - Initializing Velocity, Calling init()...
2024-12-23 14:09:38,001 - Initializing Velocity, Calling init()...
2024-12-23 14:09:38,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:09:38,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:09:38,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:09:38,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:09:38,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:09:38,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:09:38,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:09:38,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:09:38,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:09:38,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:09:38,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:09:38,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:09:38,001 - Do unicode file recognition:  false
2024-12-23 14:09:38,001 - Do unicode file recognition:  false
2024-12-23 14:09:38,001 - Do unicode file recognition:  false
2024-12-23 14:09:38,002 - FileResourceLoader : adding path '.'
2024-12-23 14:09:38,002 - FileResourceLoader : adding path '.'
2024-12-23 14:09:38,002 - FileResourceLoader : adding path '.'
2024-12-23 14:09:38,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:09:38,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:09:38,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:09:38,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:09:38,003 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:09:38,003 - Created '20' parsers.
2024-12-23 14:09:38,003 - Created '20' parsers.
2024-12-23 14:09:38,003 - Created '20' parsers.
2024-12-23 14:09:38,004 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:09:38,004 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:09:38,004 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:09:38,004 - Velocimacro : Default library not found.
2024-12-23 14:09:38,004 - Velocimacro : Default library not found.
2024-12-23 14:09:38,004 - Velocimacro : Default library not found.
2024-12-23 14:09:38,004 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:09:38,004 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:09:38,004 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:09:38,004 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:09:38,004 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:09:38,004 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:09:38,004 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:09:38,004 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:09:38,004 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:09:38,004 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:09:38,004 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:09:38,004 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:34,539 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:34,540 - Initializing Velocity, Calling init()...
2024-12-23 14:11:34,540 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:34,540 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:34,540 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:34,540 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-23 14:11:34,540 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:34,540 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:34,547 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:34,548 - Do unicode file recognition:  false
2024-12-23 14:11:34,548 - FileResourceLoader : adding path '.'
2024-12-23 14:11:34,567 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:34,570 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:34,572 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:34,573 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:34,574 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:34,575 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:34,576 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:34,578 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:34,579 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:34,581 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:34,613 - Created '20' parsers.
2024-12-23 14:11:34,616 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:34,616 - Velocimacro : Default library not found.
2024-12-23 14:11:34,616 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:34,616 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:34,616 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:34,616 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:40,261 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:40,261 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:40,262 - Initializing Velocity, Calling init()...
2024-12-23 14:11:40,262 - Initializing Velocity, Calling init()...
2024-12-23 14:11:40,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:40,262 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:40,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:40,262 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:40,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:40,262 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:40,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:11:40,262 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:11:40,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:40,262 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:40,262 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:40,262 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:40,262 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:40,262 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:40,262 - Do unicode file recognition:  false
2024-12-23 14:11:40,262 - Do unicode file recognition:  false
2024-12-23 14:11:40,262 - FileResourceLoader : adding path '.'
2024-12-23 14:11:40,262 - FileResourceLoader : adding path '.'
2024-12-23 14:11:40,262 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:40,262 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:40,263 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:40,264 - Created '20' parsers.
2024-12-23 14:11:40,264 - Created '20' parsers.
2024-12-23 14:11:40,264 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:40,264 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:40,264 - Velocimacro : Default library not found.
2024-12-23 14:11:40,264 - Velocimacro : Default library not found.
2024-12-23 14:11:40,264 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:40,264 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:40,264 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:40,264 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:40,264 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:40,264 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:40,264 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:40,264 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:47,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:47,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:47,000 - Log4JLogChute initialized using file 'velocity.log'
2024-12-23 14:11:47,001 - Initializing Velocity, Calling init()...
2024-12-23 14:11:47,001 - Initializing Velocity, Calling init()...
2024-12-23 14:11:47,001 - Initializing Velocity, Calling init()...
2024-12-23 14:11:47,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:47,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:47,001 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-23 14:11:47,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:47,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:47,001 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-23 14:11:47,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:11:47,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:11:47,001 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-23 14:11:47,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:47,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:47,001 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-23 14:11:47,001 - Do unicode file recognition:  false
2024-12-23 14:11:47,001 - Do unicode file recognition:  false
2024-12-23 14:11:47,001 - Do unicode file recognition:  false
2024-12-23 14:11:47,001 - FileResourceLoader : adding path '.'
2024-12-23 14:11:47,001 - FileResourceLoader : adding path '.'
2024-12-23 14:11:47,001 - FileResourceLoader : adding path '.'
2024-12-23 14:11:47,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:47,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:47,002 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:47,002 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-23 14:11:47,075 - Created '20' parsers.
2024-12-23 14:11:47,075 - Created '20' parsers.
2024-12-23 14:11:47,075 - Created '20' parsers.
2024-12-23 14:11:47,075 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:47,075 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:47,075 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-23 14:11:47,076 - Velocimacro : Default library not found.
2024-12-23 14:11:47,076 - Velocimacro : Default library not found.
2024-12-23 14:11:47,076 - Velocimacro : Default library not found.
2024-12-23 14:11:47,076 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:47,076 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:47,076 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-23 14:11:47,076 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:47,076 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:47,076 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-23 14:11:47,076 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:47,076 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:47,076 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-23 14:11:47,076 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:47,076 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-23 14:11:47,076 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 09:06:44,384 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 09:06:44,385 - Initializing Velocity, Calling init()...
2025-01-15 09:06:44,385 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 09:06:44,385 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 09:06:44,385 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 09:06:44,385 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-15 09:06:44,385 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:44,385 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:44,411 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 09:06:44,418 - Do unicode file recognition:  false
2025-01-15 09:06:44,418 - FileResourceLoader : adding path '.'
2025-01-15 09:06:44,616 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 09:06:44,638 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 09:06:44,645 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 09:06:44,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 09:06:44,654 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 09:06:44,658 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 09:06:44,665 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 09:06:44,671 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 09:06:44,676 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 09:06:44,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 09:06:44,981 - Created '20' parsers.
2025-01-15 09:06:44,992 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 09:06:44,992 - Velocimacro : Default library not found.
2025-01-15 09:06:44,992 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 09:06:44,992 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 09:06:44,992 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 09:06:44,992 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 09:06:48,505 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 09:06:48,505 - Initializing Velocity, Calling init()...
2025-01-15 09:06:48,505 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 09:06:48,505 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 09:06:48,505 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 09:06:48,505 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 09:06:48,505 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:48,505 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:48,505 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 09:06:48,506 - Do unicode file recognition:  false
2025-01-15 09:06:48,506 - FileResourceLoader : adding path '.'
2025-01-15 09:06:48,506 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 09:06:48,506 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 09:06:48,507 - Created '20' parsers.
2025-01-15 09:06:48,507 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 09:06:48,507 - Velocimacro : Default library not found.
2025-01-15 09:06:48,507 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 09:06:48,507 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 09:06:48,507 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 09:06:48,507 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 09:06:53,933 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 09:06:53,933 - Initializing Velocity, Calling init()...
2025-01-15 09:06:53,933 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 09:06:53,934 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 09:06:53,934 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 09:06:53,934 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 09:06:53,934 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:53,934 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 09:06:53,934 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 09:06:53,934 - Do unicode file recognition:  false
2025-01-15 09:06:53,934 - FileResourceLoader : adding path '.'
2025-01-15 09:06:53,934 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 09:06:53,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 09:06:53,936 - Created '20' parsers.
2025-01-15 09:06:53,936 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 09:06:53,936 - Velocimacro : Default library not found.
2025-01-15 09:06:53,936 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 09:06:53,936 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 09:06:53,936 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 09:06:53,936 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:44:09,222 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:44:09,223 - Initializing Velocity, Calling init()...
2025-01-15 12:44:09,223 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:44:09,223 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:44:09,223 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:44:09,223 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-15 12:44:09,223 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:09,223 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:09,246 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:44:09,253 - Do unicode file recognition:  false
2025-01-15 12:44:09,253 - FileResourceLoader : adding path '.'
2025-01-15 12:44:09,451 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:44:09,469 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:44:09,476 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:44:09,481 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:44:09,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:44:09,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:44:09,495 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:44:09,501 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:44:09,507 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:44:09,512 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:44:09,799 - Created '20' parsers.
2025-01-15 12:44:09,810 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:44:09,811 - Velocimacro : Default library not found.
2025-01-15 12:44:09,811 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:44:09,811 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:44:09,811 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:44:09,811 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:44:42,746 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:44:42,746 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:44:42,747 - Initializing Velocity, Calling init()...
2025-01-15 12:44:42,747 - Initializing Velocity, Calling init()...
2025-01-15 12:44:42,747 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:44:42,747 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:44:42,747 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:44:42,747 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:44:42,747 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:44:42,747 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:44:42,747 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 12:44:42,747 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 12:44:42,747 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:42,747 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:42,747 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:42,747 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:44:42,747 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:44:42,747 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:44:42,747 - Do unicode file recognition:  false
2025-01-15 12:44:42,747 - Do unicode file recognition:  false
2025-01-15 12:44:42,747 - FileResourceLoader : adding path '.'
2025-01-15 12:44:42,747 - FileResourceLoader : adding path '.'
2025-01-15 12:44:42,747 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:44:42,747 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:44:42,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:44:42,749 - Created '20' parsers.
2025-01-15 12:44:42,749 - Created '20' parsers.
2025-01-15 12:44:42,749 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:44:42,749 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:44:42,749 - Velocimacro : Default library not found.
2025-01-15 12:44:42,749 - Velocimacro : Default library not found.
2025-01-15 12:44:42,749 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:44:42,749 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:44:42,749 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:44:42,749 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:44:42,749 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:44:42,749 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:44:42,749 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:44:42,749 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:50:52,742 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:50:52,742 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:50:52,742 - Log4JLogChute initialized using file 'velocity.log'
2025-01-15 12:50:52,743 - Initializing Velocity, Calling init()...
2025-01-15 12:50:52,743 - Initializing Velocity, Calling init()...
2025-01-15 12:50:52,743 - Initializing Velocity, Calling init()...
2025-01-15 12:50:52,743 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:50:52,743 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:50:52,743 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-15 12:50:52,743 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:50:52,743 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:50:52,743 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-15 12:50:52,743 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 12:50:52,743 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 12:50:52,743 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-15 12:50:52,743 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:50:52,743 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:50:52,743 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-15 12:50:52,743 - Do unicode file recognition:  false
2025-01-15 12:50:52,743 - Do unicode file recognition:  false
2025-01-15 12:50:52,743 - Do unicode file recognition:  false
2025-01-15 12:50:52,743 - FileResourceLoader : adding path '.'
2025-01-15 12:50:52,743 - FileResourceLoader : adding path '.'
2025-01-15 12:50:52,743 - FileResourceLoader : adding path '.'
2025-01-15 12:50:52,744 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:50:52,744 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:50:52,744 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:50:52,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:50:52,745 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-15 12:50:52,746 - Created '20' parsers.
2025-01-15 12:50:52,746 - Created '20' parsers.
2025-01-15 12:50:52,746 - Created '20' parsers.
2025-01-15 12:50:52,746 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:50:52,746 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:50:52,746 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-15 12:50:52,746 - Velocimacro : Default library not found.
2025-01-15 12:50:52,746 - Velocimacro : Default library not found.
2025-01-15 12:50:52,746 - Velocimacro : Default library not found.
2025-01-15 12:50:52,746 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:50:52,746 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:50:52,746 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-15 12:50:52,746 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:50:52,746 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:50:52,746 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-15 12:50:52,746 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:50:52,746 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:50:52,746 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-15 12:50:52,746 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:50:52,746 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-15 12:50:52,746 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-01-17 15:40:49,344 - Log4JLogChute initialized using file 'velocity.log'
2025-01-17 15:40:49,345 - Initializing Velocity, Calling init()...
2025-01-17 15:40:49,345 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-01-17 15:40:49,345 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-01-17 15:40:49,345 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-01-17 15:40:49,345 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-01-17 15:40:49,345 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-17 15:40:49,345 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-01-17 15:40:49,370 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-01-17 15:40:49,377 - Do unicode file recognition:  false
2025-01-17 15:40:49,377 - FileResourceLoader : adding path '.'
2025-01-17 15:40:49,562 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-01-17 15:40:49,580 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-01-17 15:40:49,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-01-17 15:40:49,590 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-01-17 15:40:49,593 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-01-17 15:40:49,597 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-01-17 15:40:49,603 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-01-17 15:40:49,609 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-01-17 15:40:49,613 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-01-17 15:40:49,618 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-01-17 15:40:49,899 - Created '20' parsers.
2025-01-17 15:40:49,918 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-01-17 15:40:49,918 - Velocimacro : Default library not found.
2025-01-17 15:40:49,918 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-01-17 15:40:49,918 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-01-17 15:40:49,918 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-01-17 15:40:49,918 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 14:55:09,447 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 14:55:09,455 - Initializing Velocity, Calling init()...
2025-02-06 14:55:09,455 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 14:55:09,455 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 14:55:09,455 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 14:55:09,455 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-02-06 14:55:09,455 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 14:55:09,455 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 14:55:09,483 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 14:55:09,491 - Do unicode file recognition:  false
2025-02-06 14:55:09,491 - FileResourceLoader : adding path '.'
2025-02-06 14:55:09,730 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 14:55:09,751 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 14:55:09,763 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 14:55:09,772 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 14:55:09,779 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 14:55:09,785 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 14:55:09,796 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 14:55:09,802 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 14:55:09,808 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 14:55:09,814 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 14:55:10,168 - Created '20' parsers.
2025-02-06 14:55:10,187 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 14:55:10,187 - Velocimacro : Default library not found.
2025-02-06 14:55:10,187 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 14:55:10,187 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 14:55:10,188 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 14:55:10,188 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 14:58:50,529 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 14:58:50,537 - Initializing Velocity, Calling init()...
2025-02-06 14:58:50,537 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 14:58:50,537 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 14:58:50,537 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 14:58:50,537 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 14:58:50,537 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 14:58:50,537 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 14:58:50,537 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 14:58:50,537 - Do unicode file recognition:  false
2025-02-06 14:58:50,537 - FileResourceLoader : adding path '.'
2025-02-06 14:58:50,537 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 14:58:50,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 14:58:50,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 14:58:50,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 14:58:50,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 14:58:50,539 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 14:58:50,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 14:58:50,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 14:58:50,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 14:58:50,540 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 14:58:50,540 - Created '20' parsers.
2025-02-06 14:58:50,540 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 14:58:50,540 - Velocimacro : Default library not found.
2025-02-06 14:58:50,540 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 14:58:50,540 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 14:58:50,540 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 14:58:50,540 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:00:42,439 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:00:42,439 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:00:42,440 - Initializing Velocity, Calling init()...
2025-02-06 15:00:42,440 - Initializing Velocity, Calling init()...
2025-02-06 15:00:42,440 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:00:42,440 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:00:42,440 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:00:42,440 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:00:42,440 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:00:42,440 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:00:42,440 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:00:42,440 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:00:42,440 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:42,440 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:42,440 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:42,440 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:42,440 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:00:42,440 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:00:42,440 - Do unicode file recognition:  false
2025-02-06 15:00:42,440 - Do unicode file recognition:  false
2025-02-06 15:00:42,440 - FileResourceLoader : adding path '.'
2025-02-06 15:00:42,440 - FileResourceLoader : adding path '.'
2025-02-06 15:00:42,440 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:00:42,440 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:00:42,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:00:42,442 - Created '20' parsers.
2025-02-06 15:00:42,442 - Created '20' parsers.
2025-02-06 15:00:42,442 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:00:42,442 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:00:42,442 - Velocimacro : Default library not found.
2025-02-06 15:00:42,442 - Velocimacro : Default library not found.
2025-02-06 15:00:42,442 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:00:42,442 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:00:42,442 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:00:42,442 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:00:42,442 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:00:42,442 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:00:42,442 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:00:42,442 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:00:42,456 - Null reference [template '', line 38, column 89] : ${object.itemname} cannot be resolved.
2025-02-06 15:00:42,456 - Null reference [template '', line 38, column 89] : ${object.itemname} cannot be resolved.
2025-02-06 15:00:42,456 - Null reference [template '', line 38, column 142] : ${object.itemspec} cannot be resolved.
2025-02-06 15:00:42,456 - Null reference [template '', line 38, column 142] : ${object.itemspec} cannot be resolved.
2025-02-06 15:00:46,267 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:00:46,267 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:00:46,267 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:00:46,268 - Initializing Velocity, Calling init()...
2025-02-06 15:00:46,268 - Initializing Velocity, Calling init()...
2025-02-06 15:00:46,268 - Initializing Velocity, Calling init()...
2025-02-06 15:00:46,268 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:00:46,268 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:00:46,268 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:00:46,268 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:00:46,268 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:00:46,268 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:00:46,268 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:00:46,268 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:00:46,268 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,268 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,268 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,268 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,268 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:00:46,269 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:00:46,269 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:00:46,269 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:00:46,269 - Do unicode file recognition:  false
2025-02-06 15:00:46,269 - Do unicode file recognition:  false
2025-02-06 15:00:46,269 - Do unicode file recognition:  false
2025-02-06 15:00:46,269 - FileResourceLoader : adding path '.'
2025-02-06 15:00:46,269 - FileResourceLoader : adding path '.'
2025-02-06 15:00:46,269 - FileResourceLoader : adding path '.'
2025-02-06 15:00:46,269 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:00:46,269 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:00:46,269 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:00:46,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:00:46,272 - Created '20' parsers.
2025-02-06 15:00:46,272 - Created '20' parsers.
2025-02-06 15:00:46,272 - Created '20' parsers.
2025-02-06 15:00:46,272 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:00:46,272 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:00:46,272 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:00:46,272 - Velocimacro : Default library not found.
2025-02-06 15:00:46,272 - Velocimacro : Default library not found.
2025-02-06 15:00:46,272 - Velocimacro : Default library not found.
2025-02-06 15:00:46,272 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:00:46,272 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:00:46,272 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:00:46,272 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:00:46,272 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:00:46,272 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:00:46,272 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:00:46,272 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:00:46,272 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:00:46,272 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:00:46,272 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:00:46,272 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:14:06,088 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:14:06,088 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:14:06,088 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:14:06,088 - Log4JLogChute initialized using file 'velocity.log'
2025-02-06 15:14:06,088 - Initializing Velocity, Calling init()...
2025-02-06 15:14:06,088 - Initializing Velocity, Calling init()...
2025-02-06 15:14:06,088 - Initializing Velocity, Calling init()...
2025-02-06 15:14:06,088 - Initializing Velocity, Calling init()...
2025-02-06 15:14:06,088 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:14:06,088 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:14:06,088 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:14:06,088 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-02-06 15:14:06,088 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:14:06,088 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:14:06,088 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:14:06,088 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-02-06 15:14:06,088 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:14:06,088 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:14:06,088 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:14:06,088 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-02-06 15:14:06,088 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:14:06,088 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:14:06,088 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:14:06,088 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-02-06 15:14:06,088 - Do unicode file recognition:  false
2025-02-06 15:14:06,088 - Do unicode file recognition:  false
2025-02-06 15:14:06,088 - Do unicode file recognition:  false
2025-02-06 15:14:06,088 - Do unicode file recognition:  false
2025-02-06 15:14:06,088 - FileResourceLoader : adding path '.'
2025-02-06 15:14:06,088 - FileResourceLoader : adding path '.'
2025-02-06 15:14:06,088 - FileResourceLoader : adding path '.'
2025-02-06 15:14:06,088 - FileResourceLoader : adding path '.'
2025-02-06 15:14:06,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:14:06,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:14:06,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:14:06,089 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:14:06,089 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-02-06 15:14:06,090 - Created '20' parsers.
2025-02-06 15:14:06,090 - Created '20' parsers.
2025-02-06 15:14:06,090 - Created '20' parsers.
2025-02-06 15:14:06,090 - Created '20' parsers.
2025-02-06 15:14:06,090 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:14:06,090 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:14:06,090 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:14:06,090 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-02-06 15:14:06,091 - Velocimacro : Default library not found.
2025-02-06 15:14:06,091 - Velocimacro : Default library not found.
2025-02-06 15:14:06,091 - Velocimacro : Default library not found.
2025-02-06 15:14:06,091 - Velocimacro : Default library not found.
2025-02-06 15:14:06,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:14:06,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:14:06,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:14:06,091 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-02-06 15:14:06,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:14:06,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:14:06,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:14:06,091 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-02-06 15:14:06,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:14:06,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:14:06,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:14:06,091 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-02-06 15:14:06,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:14:06,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:14:06,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-02-06 15:14:06,091 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-23 16:50:49,005 - Log4JLogChute initialized using file 'velocity.log'
